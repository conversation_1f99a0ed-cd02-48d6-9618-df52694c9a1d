"""
Video Metadata Agent
-------------------
Generates video titles, descriptions, and hashtags based on story content and trending keywords.
"""

import json
import logging
from typing import List

from crewai import Task, Crew, Process

from utils.parsers import VideoMetadataParser
from inference.dataforseo_client import DataForSEOClient
from utils.agent_factory import create_rate_limited_agent
from models.schema import Story, VideoMetadata, TrendingKeyword

logger = logging.getLogger(__name__)


class VideoMetadataAgent:
    """
    Agent for generating video metadata including titles, descriptions, and hashtags.
    
    This agent analyzes story content, generates relevant keywords, checks trending data
    from DataForSEO, and creates optimized video metadata for social media platforms.
    """
    
    def __init__(self, 
                 verbose: bool = False,
                 model: str = "gpt-4o-mini",
                 provider: str = "openai") -> None:
        """
        Initialize the Video Metadata Agent.
        
        Args:
            verbose (bool): Whether to enable verbose output from CrewAI. Defaults to False.
            model (str): The model to use for the agent. Defaults to "gpt-4o-mini".
            provider (str): The LLM provider to use. Defaults to "openai".
        """
        self.verbose = verbose
        self.model = model
        self.provider = provider
        
        # Initialize DataForSEO client
        self.dataforseo_client = DataForSEOClient()
        
        logger.info(f"VideoMetadataAgent initialized with model: {model}, provider: {provider}")
    
    def generate_video_metadata(self, story: Story) -> VideoMetadata:
        """
        Generate comprehensive video metadata based on story content and trending keywords.
        
        Args:
            story (Story): The complete story with scenes
            
        Returns:
            VideoMetadata: Complete video metadata including title, description, and hashtags
        """
        logger.info(f"Generating video metadata for story: {story.title}")
        
        # Step 1: Generate initial keywords from story content
        initial_keywords = self._generate_keywords_from_story(story)
        
        # Step 2: Get trending data from DataForSEO
        trending_keywords = self._get_trending_keywords(initial_keywords)
        
        # Step 3: Generate final video metadata using trending keywords
        video_metadata = self._generate_final_metadata(story, trending_keywords)
        
        logger.info("Video metadata generation completed successfully")
        return video_metadata
    
    def _generate_keywords_from_story(self, story: Story) -> List[str]:
        """
        Generate 100 keywords from the story content.
        
        Args:
            story (Story): The story to analyze
            
        Returns:
            List[str]: List of generated keywords
        """
        logger.info("Generating keywords from story content")
        
        # Create the keyword generation agent
        keyword_agent = create_rate_limited_agent(
            role="SEO Keyword Research Specialist",
            goal="Generate comprehensive keywords for video content optimization based on story analysis",
            backstory="""You are an expert SEO and digital marketing specialist with extensive experience in 
            keyword research and content optimization for video platforms like YouTube and social media.
            
            Your expertise includes:
            - Analyzing content to identify relevant keywords and phrases
            - Understanding search intent and user behavior
            - Generating keywords that balance search volume with relevance
            - Creating keyword variations and long-tail keywords
            - Identifying trending topics and viral content patterns
            - Understanding Hindi and English keyword dynamics for Indian audiences
            
            You specialize in generating keywords that are:
            - Highly relevant to the content
            - Likely to be searched by target audiences
            - Balanced between competitive and niche terms
            - Culturally appropriate for Indian audiences
            - Optimized for video discovery and engagement
            
            Your keyword research helps content creators maximize their reach and engagement.""",
            model=self.model,
            provider=self.provider,
            verbose=self.verbose,
            allow_delegation=False
        )
        
        # Combine all story content for analysis
        story_content = f"Title: {story.title}\n\n"
        for scene in story.scenes:
            story_content += f"Scene {scene.scene_number}:\n"
            story_content += f"Narration: {scene.narration}\n\n"
        
        # Create the keyword generation task
        keyword_task = Task(
            description=f"""Analyze the following Hindi story content and generate exactly 100 relevant English keywords
            that could be used for video SEO and social media optimization:

            STORY CONTENT:
            {story_content}

            Generate 100 English keywords that include:
            1. Main topic keywords (10-15 keywords)
            2. Character and people-related keywords (10-15 keywords)
            3. Location and place keywords (10-15 keywords)
            4. Event and action keywords (15-20 keywords)
            5. Emotional and descriptive keywords (10-15 keywords)
            6. Documentary and storytelling keywords (10-15 keywords)
            7. Indian culture and context keywords (10-15 keywords)
            8. Long-tail keyword phrases (10-15 keywords)

            Requirements:
            - ALL keywords must be in English and relevant to the story content
            - Include both broad and specific terms
            - Mix of single words and short phrases (2-4 words)
            - Consider trending topics and popular search terms
            - Include character names and key people mentioned
            - Focus on terms that would help the video be discovered by interested viewers
            - Consider SEO best practices for video content
            - Use English terms that would be searched by Hindi content viewers

            Return the keywords as a simple JSON array of strings, exactly 100 English keywords total.
            Example format: ["keyword1", "keyword2", "keyword3", ...]""",
            agent=keyword_agent,
            expected_output="A JSON array containing exactly 100 relevant keywords as strings"
        )
        
        # Execute the task
        crew = Crew(
            agents=[keyword_agent],
            tasks=[keyword_task],
            process=Process.sequential,
            verbose=self.verbose
        )
        
        try:
            result = crew.kickoff()
            
            # Extract keywords from the result
            if hasattr(result, 'raw'):
                keywords_text = result.raw
            else:
                keywords_text = str(result)
            
            # Parse JSON array of keywords
            try:
                # Extract JSON array from the text
                import re
                json_match = re.search(r'\[[\s\S]*\]', keywords_text)
                if json_match:
                    keywords_json = json_match.group(0)
                    keywords = json.loads(keywords_json)
                    
                    if isinstance(keywords, list) and len(keywords) > 0:
                        logger.info(f"Generated {len(keywords)} keywords from story content")
                        return keywords[:100]  # Ensure we don't exceed 100 keywords
                
                # Fallback: split by common delimiters
                keywords = []
                for line in keywords_text.split('\n'):
                    line = line.strip()
                    if line and not line.startswith('[') and not line.startswith(']'):
                        # Remove quotes, numbers, and clean up
                        clean_line = re.sub(r'^[\d\.\-\*\s]*["\']?', '', line)
                        clean_line = re.sub(r'["\']?\s*,?\s*$', '', clean_line)
                        if clean_line:
                            keywords.append(clean_line)
                
                if keywords:
                    logger.info(f"Generated {len(keywords)} keywords using fallback parsing")
                    return keywords[:100]
                
            except Exception as parse_e:
                logger.warning(f"Error parsing keywords JSON: {str(parse_e)}")
            
            # Final fallback: return some basic English keywords
            logger.warning("Using fallback keywords based on story title")
            return [story.title, "documentary", "real incident", "Indian story", "true story", "Hindi content"]

        except Exception as e:
            logger.error(f"Error generating keywords from story: {str(e)}")
            return [story.title, "documentary", "true story"]

    def _get_trending_keywords(self, keywords: List[str]) -> List[TrendingKeyword]:
        """
        Get trending keywords from DataForSEO based on generated keywords.

        Args:
            keywords (List[str]): List of keywords to check for trends

        Returns:
            List[TrendingKeyword]: List of trending keywords with trend data
        """
        logger.info(f"Checking trends for {len(keywords)} keywords")

        try:
            # DataForSEO has limits, so we'll check keywords in batches
            # and select the most relevant ones
            batch_size = 5  # DataForSEO typically allows 5 keywords per request
            trending_keywords = []

            # Process keywords in batches
            for i in range(0, min(len(keywords), 25), batch_size):  # Check up to 25 keywords
                batch = keywords[i:i+batch_size]

                try:
                    # Get trends data from DataForSEO
                    trends_response = self.dataforseo_client.get_keyword_trends(
                        keywords=batch,
                        time_range="past_7_days",
                        location_code=2356,  # India
                        language_code="en",
                        search_type="youtube"
                    )

                    # Extract trending keywords from response
                    batch_trending = self.dataforseo_client.extract_trending_keywords(
                        trends_response,
                        min_trend_value=5,  # Lower threshold to get more results
                        max_keywords=10
                    )

                    # Convert to TrendingKeyword objects
                    for trend_data in batch_trending:
                        trending_keyword = TrendingKeyword(
                            keyword=trend_data["keyword"],
                            average_trend=trend_data["average_trend"],
                            max_trend=trend_data["max_trend"],
                            data_points=trend_data["data_points"]
                        )
                        trending_keywords.append(trending_keyword)

                except Exception as batch_e:
                    logger.warning(f"Error processing keyword batch {batch}: {str(batch_e)}")
                    continue

            # Sort by average trend and return top 15-20
            trending_keywords.sort(key=lambda x: x.average_trend, reverse=True)
            final_trending = trending_keywords[:20]

            logger.info(f"Found {len(final_trending)} trending keywords")
            return final_trending

        except Exception as e:
            logger.error(f"Error getting trending keywords: {str(e)}")
            # Return empty list if trending analysis fails
            return []

    def _generate_final_metadata(self, story: Story, trending_keywords: List[TrendingKeyword]) -> VideoMetadata:
        """
        Generate final video metadata using story content and trending keywords.

        Args:
            story (Story): The complete story
            trending_keywords (List[TrendingKeyword]): List of trending keywords

        Returns:
            VideoMetadata: Complete video metadata
        """
        logger.info("Generating final video metadata")

        # Create the metadata generation agent
        metadata_agent = create_rate_limited_agent(
            role="Video Content Marketing Specialist",
            goal="Create compelling video titles, descriptions, and hashtags optimized for maximum engagement and discoverability",
            backstory="""You are an expert video content marketing specialist and social media strategist with
            extensive experience in creating viral content for YouTube, Instagram, and other video platforms.

            Your expertise includes:
            - Creating compelling, click-worthy video titles that drive engagement
            - Writing detailed video descriptions that improve SEO and viewer retention
            - Generating trending hashtags that maximize content discoverability
            - Understanding YouTube and social media algorithms
            - Optimizing content for Indian audiences and Hindi content
            - Balancing clickbait appeal with authentic, valuable content
            - Creating content that encourages sharing and engagement
            - Understanding trending topics and viral content patterns

            You specialize in creating metadata that:
            - Captures attention and encourages clicks
            - Accurately represents the content
            - Incorporates trending keywords naturally
            - Appeals to target demographics
            - Maximizes algorithmic visibility
            - Encourages social sharing and engagement

            Your content helps creators build audiences and achieve viral success while maintaining authenticity.""",
            model=self.model,
            provider=self.provider,
            verbose=self.verbose,
            allow_delegation=False
        )

        # Prepare trending keywords data
        trending_keywords_text = ""
        if trending_keywords:
            trending_keywords_text = "TRENDING KEYWORDS TO INCORPORATE:\n"
            for tk in trending_keywords:
                trending_keywords_text += f"- {tk.keyword} (trend score: {tk.average_trend})\n"
        else:
            trending_keywords_text = "No specific trending keywords available - use general optimization strategies."

        # Combine story content
        story_content = f"Title: {story.title}\n\n"
        story_content += "Story Content:\n"
        for scene in story.scenes:
            story_content += f"Scene {scene.scene_number}: {scene.narration}\n\n"

        # Create a parser for the VideoMetadata model
        parser = VideoMetadataParser.create()
        format_instructions = parser.get_format_instructions()

        # Create the metadata generation task
        metadata_task = Task(
            description=f"""Create comprehensive video metadata for a Hindi documentary story based on the following content:

            {story_content}

            {trending_keywords_text}

            Generate optimized video metadata in English that includes:

            1. **Video Title**: Create a compelling, click-worthy title in english that:
               - Is 60 characters or less for optimal display
               - Incorporates trending keywords naturally
               - Creates curiosity and encourages clicks
               - Accurately represents the content
               - Appeals to Hindi/Indian audiences
               - Uses emotional triggers or compelling phrases

            2. **Short Description**: Write a brief 2-3 sentence description in english that:
               - Summarizes the key story elements
               - Creates intrigue without spoiling the story
               - Incorporates relevant keywords

            3. **Detailed Description**: Create a comprehensive description in english that:
               - Provides more context about the story
               - Includes relevant keywords naturally
               - Encourages engagement (likes, comments, shares)
               - Includes call-to-action elements
               - Is 200-300 words long

            4. **Hashtags**: Generate 25-30 relevant hashtags in english that:
               - Include trending keywords from the provided list
               - Mix popular and niche hashtags
               - Include only English hashtags
               - Are relevant to the story content
               - Follow hashtag best practices

            5. **Additional Metadata**: Include:
               - Target audience description
               - Content category
               - Estimated watch time

            Requirements:
            - Optimize for Indian/Hindi-speaking audiences
            - Incorporate trending keywords naturally (don't force them)
            - Balance SEO optimization with authentic, engaging content
            - Create content that encourages sharing and engagement
            - Ensure all content is appropriate and family-friendly""",
            agent=metadata_agent,
            expected_output=format_instructions
        )

        # Execute the task
        crew = Crew(
            agents=[metadata_agent],
            tasks=[metadata_task],
            process=Process.sequential,
            verbose=self.verbose
        )

        try:
            result = crew.kickoff()

            # Extract the result
            if hasattr(result, 'raw'):
                metadata_text = result.raw
            else:
                metadata_text = str(result)

            # Parse the result using the Pydantic parser
            video_metadata = VideoMetadataParser.parse_output(parser, metadata_text)

            if video_metadata is None:
                logger.error("Could not parse video metadata result")
                raise ValueError("Failed to parse video metadata from agent")

            # Add trending keywords used to the metadata
            if trending_keywords:
                video_metadata.trending_keywords_used = [tk.keyword for tk in trending_keywords]

            logger.info("Video metadata generation completed successfully")
            return video_metadata

        except Exception as e:
            logger.error(f"Error generating final metadata: {str(e)}")
            raise
